import PageHeader from "@/components/general/PageHeader";
import { ProductDetailDefaultMeasurementCard } from "@/components/product/productDetail/ProductDetailDefaultMeasurmentCard";
import { ProductDetailsDefaultMeasurementChart } from "@/components/product/productDetail/ProductDetailsDefauldMeasurmentChart";
import { Button } from "@/components/ui/button";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import { Edit, TrashIcon } from "lucide-react";
import React, { use } from "react";

interface ProductDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = use(params);
  console.log(id);
  return (
    <PageWrapper>
      <PageHeader
        title="Szczegóły produktu"
        actions={
          <>
            <Button size={"icon"} variant={"outline"}>
              <Edit className="w-4 h-4" />
            </Button>
            <Button size={"icon"} variant={"outline"} className="text-destructive hover:text-destructive">
              <TrashIcon className="w-4 h-4" />
            </Button>
          </>
        }
      />
      <div className="p-4 grid grid-cols-1 lg:grid-cols-2 gap-4 lg:items-start overflow-auto">
        <ProductDetailDefaultMeasurementCard calories={100} protein={10} carbs={10} fat={10} unit={"g"} />
        <ProductDetailsDefaultMeasurementChart calories={100} protein={10} carbs={10} fat={10} />
      </div>
    </PageWrapper>
  );
}
